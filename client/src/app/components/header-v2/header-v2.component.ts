import { Component, EventEmitter, Inject, inject, input, Output, PLATFORM_ID, effect, signal, HostListener, viewChild, ElementRef } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { ActivatedRoute, Router, RouterLink } from "@angular/router";
import { TranslocoService } from "@jsverse/transloco";
import { ContentService } from "@/services/content.service";
import { ToasterService } from '@/services/toaster.service';
import { LibraryService } from '@/services/library.service';
import { ProfileService } from "@/services/profile.service";
import { CommonModule, NgOptimizedImage } from '@angular/common';
import { AuthService } from '@/services/auth.service';
import {environment} from "@/env/environment";
import { ClickOutsideDirective } from "@/directives/clickOutside";
import headerDropdownMenuData from './config/header-dropdown-menu.json';
import headerBurgerMenuData from './config/header-burger-menu.json';

export enum MenuLinkMenuKeys {
  tradition = 'tradition',
  education = 'education',
  practice = 'practice',
  library = 'library',
  events = 'events',
}

@Component({
  selector: 'app-header-v2',
  standalone: true,
  imports: [RouterLink, CommonModule, NgOptimizedImage, ClickOutsideDirective],
  templateUrl: './header-v2.component.html',
  styleUrl: './header-v2.component.scss'
})
export class HeaderV2Component {
  readonly menuLinksArrays: Record<MenuLinkMenuKeys, any[]> = headerDropdownMenuData.menuLinksArrays;
  readonly isScrolled = input<boolean>(false);
  readonly isBurgerMenuOpen = signal<boolean>(false);
  isUserMenuOpen = signal<boolean>(false);
  activeLinkMenu = signal<MenuLinkMenuKeys | null>(null);
  readonly isMobileScreen = signal<boolean>(false);
  private readonly burgerMenuContent = viewChild<ElementRef<HTMLElement>>('burgerMenuContent');

  selectedLinkMenu = signal<any[]>([]);
  readonly MenuLinkMenuKeys = MenuLinkMenuKeys;
  readonly burgrerMenu = signal(structuredClone(headerBurgerMenuData));

  // searchShow: boolean = false;
  @Output() sideOpen = new EventEmitter<any>();
  @Output() sideClose = new EventEmitter<any>();
  // @Input() isSidebarOpen = false;
  route = inject(ActivatedRoute)
  router = inject(Router);
  contentService = inject(ContentService);
  libraryService = inject(LibraryService);
  translocoService = inject(TranslocoService);
  profileService = inject(ProfileService);
  authService = inject(AuthService);
  currentLanguage = signal<string>('ru');
  environment = environment;
  
  @HostListener('window:resize')
  checkScreenSize(): void {
     if (isPlatformBrowser(this.platformId)) {
      const wasMobile = this.isMobileScreen();
      const isNowMobile = window.innerWidth <= 680;
      
      this.isMobileScreen.set(isNowMobile);
      
      // Закрываем меню при любом переходе между мобильным и десктопным экраном
      if (wasMobile !== isNowMobile && this.isBurgerMenuOpen()) {
        this.closeBurgerMenu();
      }
    }
  }

  constructor(
    @Inject(PLATFORM_ID) private platformId: any,
    private toasterService: ToasterService
  ) {
    effect(() => {
      if (this.authService.token() && !this.profileService.name()) {
        this.profileService.getProfile().subscribe();
      } 
    });
    this.checkScreenSize();
   }

  ngOnInit() {
    // this.contentService.getAll().subscribe();
    // this.libraryService.getAll().subscribe();
  }

  // toggleSearchPanel() {
  //   this.searchShow = !this.searchShow;
  // }

  async changeLanguage(e: any) {
    const currentUrl = this.router.url
    const newUrl = currentUrl.replace(`/${this.translocoService.getActiveLang()}/`, `/${e.target.value}/`)
    this.router.navigate([newUrl])
  }

  toggleSidebar() {
    this.sideOpen.emit(true);
  }

  navigateToPhoto() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/photo`]);
    this.sideOpen.emit(false);
  }

  navigateToAudio() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/audiogallery/audiolektsii`]);
    this.sideOpen.emit(false);
  }

  navigateToForum() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/forum`]);
    this.sideOpen.emit(false);
  }


  navigateToLibrary() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/library`]);
    this.sideOpen.emit(false);
  }

  navigateToMain() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/`]);
    this.sideOpen.emit(false);
  }

  navigateToCatigory() {
    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/categories`]);
    this.sideOpen.emit(false);
  }

  toggleUserMenu(event: Event) {
    event.stopPropagation();
    this.isUserMenuOpen.set(!this.isUserMenuOpen());
  }

  closeUserMenu() {
    this.isUserMenuOpen.set(false);
  }

  navigateToProfile() {
    this.router.navigate(['/ru/profile']);
    this.closeUserMenu();
    this.sideClose.emit(true);
  }

  navigateToFavorites() {
    this.router.navigate(['/ru/profile/favorites']);
    this.closeUserMenu();
    this.sideClose.emit(true);
  }

  navigateToPlaylists() {
    this.router.navigate(['/ru/profile/playlists']);
    this.closeUserMenu();
    this.sideClose.emit(true);
  }

  navigateToMyData() {
    this.router.navigate(['/ru/profile/my-data']);
    this.closeUserMenu();
    this.sideClose.emit(true);
  }

  navigateToAnketa() {
    this.router.navigate(['/ru/anketa']);
    this.closeUserMenu();
    this.sideClose.emit(true);
  }

  navigateToSubscriptions() {
    this.router.navigate(['/ru/profile/subscriptions']);
    this.closeUserMenu();
    this.sideClose.emit(true);
  }

  navigateToDonation() {
    this.router.navigate(['/ru/donation/bank-transfer']);
    this.closeUserMenu();
    this.sideClose.emit(true);
  }

  navigateToAIChat() {
    this.router.navigate(['/ru/ai-chat']);
    this.closeUserMenu();
    this.sideClose.emit(true);
  }
  
  
  logout() {
    this.authService.logout();
    this.closeUserMenu();
    this.sideClose.emit(true);
  }



  toggleLinkMemu(key: MenuLinkMenuKeys) {
    if (this.activeLinkMenu() === key) {
      this.closeLinkMemu();
    } else {
      this.showLinkMemu(key);
    }
  }

  showLinkMemu(key: MenuLinkMenuKeys) {
    if (!key) return;
    this.activeLinkMenu.set(key)
    this.selectedLinkMenu.set(this.menuLinksArrays[key]);
  }

  closeLinkMemu() {
    this.activeLinkMenu.set(null)
    this.selectedLinkMenu.set([]);
  }

  toggleBurgerMenu() {
    this.isBurgerMenuOpen.set(!this.isBurgerMenuOpen());
    this.burgrerMenu.set(structuredClone(headerBurgerMenuData));
    this.closeLinkMemu();
  }

  navigateToHome() {
    this.closeLinkMemu();
    this.navigateToMain();
  }

  // navigateToForum() {
  //   this.router.navigate(['/ru/anketa']);
  //   this.closeUserMenu();
  //   this.sideClose.emit(true);
  // }

  selectBurgerMenuCol(section: any) {
    this.burgrerMenu().forEach((section: any) => {
      section.activeSection = false;
    });
    section.activeSection = !section.activeSection;

    if (!this.isMobileScreen()) {
      setTimeout(() => {
        this.scrollToActiveSection();
      }, 0);
    }
  }

  private scrollToActiveSection() {
    const containerRef = this.burgerMenuContent();
    if (!containerRef) return;

    const container = containerRef.nativeElement;
    const activeSection = container.querySelector('section.active') as HTMLElement;
    
    if (activeSection && container) {
      const containerRect = container.getBoundingClientRect();
      const sectionRect = activeSection.getBoundingClientRect();
      const scrollTop = container.scrollTop + (sectionRect.top - containerRect.top);
      
      container.scrollTo({
        top: scrollTop,
        behavior: 'smooth'
      });
    }
  }


  closeBurgerMenu() {
    this.isBurgerMenuOpen.set(false);
  }

  navigateToBurgerMenuLink(url: string) {
    this.closeBurgerMenu();
    this.closeLinkMemu();
    if (!url) return;

    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/${url}`]);
  }

  navigateToDropdownMenuLink(url: string) {
    this.closeLinkMemu();
    if (!url) return;

    const decodedUrl = decodeURIComponent(this.router.url);
    const baseRoute = decodedUrl.split('/')[1];
    this.router.navigate([`/${baseRoute}/${url}`]);
  }

  toggleBurgerMenuSection(selectedSection: any) {
    if (!this.isMobileScreen()) return;
    this.burgrerMenu().forEach((section: any) => {
      if (selectedSection.sectionName !== section.sectionName) {
        section.activeSection = false;
        section.linkCols.forEach((col: any) => {
          col.activeCol = false;
        });
      }
    });
    selectedSection.activeSection = !selectedSection.activeSection;
  }

  toggleBurgerMenuSectionCol(section: any, col: any) {
    if (!this.isMobileScreen()) return;

    if (!section && section.linkCols?.length) return;
    section.linkCols.forEach((linksCol: any) => {
      if (linksCol.colName !== col.colName) {
        linksCol.activeCol = false;
      }
    });
    col.activeCol = !col.activeCol;
  }

  navToSocialMedia() {
    // TODO: add logic for navigation social media
  }

  showSidebar() {
    this.sideOpen.emit(true);
  }
}
