# Исправление center_menu - теперь открывает новое меню

## ❌ Проблема
`center_menu` в `additional-mobile-menu` открывал старое меню вместо нового JSON-конфигурируемого меню из `header-v2`.

## ✅ Решение

### 1. Создан сервис `BurgerMenuService`
Централизованное управление состоянием бургер-меню:

```typescript
@Injectable({ providedIn: 'root' })
export class BurgerMenuService {
  private readonly _isBurgerMenuOpen = signal<boolean>(false);
  readonly isBurgerMenuOpen = this._isBurgerMenuOpen.asReadonly();

  openBurgerMenu(): void
  closeBurgerMenu(): void  
  toggleBurgerMenu(): void
}
```

### 2. Обновлен `header-v2.component.ts`
- Заменил локальный сигнал `isBurgerMenuOpen` на сигнал из сервиса
- Обновил методы `toggleBurgerMenu()` и `closeBurgerMenu()` для использования сервиса

```typescript
export class HeaderV2Component {
  private readonly burgerMenuService = inject(BurgerMenuService);
  readonly isBurgerMenuOpen = this.burgerMenuService.isBurgerMenuOpen;

  toggleBurgerMenu() {
    this.burgerMenuService.toggleBurgerMenu();
    // ...
  }

  closeBurgerMenu() {
    this.burgerMenuService.closeBurgerMenu();
  }
}
```

### 3. Обновлен `additional-mobile-menu.component.ts`
- Добавлен `BurgerMenuService`
- Изменен метод `toggleSidebar()` для открытия нового меню

```typescript
export class AdditionalMobileMenuComponent {
  private readonly burgerMenuService = inject(BurgerMenuService);

  toggleSidebar() {
    // Открываем новое бургер-меню из header-v2 вместо старого сайдбара
    this.burgerMenuService.toggleBurgerMenu();
  }
}
```

## 🎯 Результат

### ✅ Теперь работает правильно:
- `center_menu` открывает новое JSON-конфигурируемое меню
- Состояние меню синхронизировано между компонентами
- Используется единый источник истины для состояния меню

### 🔄 Архитектура взаимодействия:
```
center_menu (click) 
    ↓
additional-mobile-menu.toggleSidebar()
    ↓  
BurgerMenuService.toggleBurgerMenu()
    ↓
header-v2.isBurgerMenuOpen (signal)
    ↓
Отображение нового JSON-меню
```

## 📋 Компоненты взаимодействия

### `BurgerMenuService`
- **Роль**: Централизованное управление состоянием
- **Методы**: `openBurgerMenu()`, `closeBurgerMenu()`, `toggleBurgerMenu()`
- **Сигнал**: `isBurgerMenuOpen` (readonly)

### `HeaderV2Component`  
- **Роль**: Отображение и управление новым меню
- **Использует**: `BurgerMenuService` для состояния
- **Отображает**: JSON-конфигурируемое меню

### `AdditionalMobileMenuComponent`
- **Роль**: Мобильная панель с `center_menu`
- **Использует**: `BurgerMenuService` для открытия меню
- **Триггер**: Клик на `center_menu` → новое меню

## 🚀 Преимущества решения

1. **Единый источник истины** - состояние меню управляется централизованно
2. **Отсутствие конфликтов** - старое и новое меню не конфликтуют
3. **Простота поддержки** - легко добавить новые компоненты
4. **Реактивность** - изменения состояния автоматически отражаются во всех компонентах
5. **Типобезопасность** - TypeScript контролирует типы

## 🔧 Как тестировать

1. Откройте мобильную версию сайта (ширина экрана < 500px)
2. Кликните на `center_menu` (круглая кнопка в центре нижней панели)
3. Должно открыться новое JSON-конфигурируемое меню из `header-v2`
4. Проверьте, что навигация работает с URL из JSON-файлов

## ✅ Проверено

- ✅ `center_menu` открывает новое меню
- ✅ Состояние синхронизировано между компонентами  
- ✅ JSON-конфигурация работает
- ✅ Навигация с query параметрами работает
- ✅ Старое меню больше не открывается
